import { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import {
  FaUser, FaGamepad, FaCog, FaHeart,
  FaSignOutAlt, FaChevronRight
} from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import { useLanguageNavigation } from '../hooks/useLanguageNavigation';
import LoadingSpinner from '../components/LoadingSpinner';
import { getSecureImageUrl } from '../utils/imageUtils';
import { API_URL } from '../config/env.js';

// Import Profile Components
import OverviewSection from '../components/Profile/OverviewSection';
import SettingsSection from '../components/Profile/SettingsSection';
import FavoritesSection from '../components/Profile/FavoritesSection';
import UploadsSection from '../components/Profile/UploadsSection';

const ProfilePage = () => {
  const { user, logout, loading: authLoading } = useAuth();
  const { createLanguageLink } = useLanguageNavigation();
  const [activeSection, setActiveSection] = useState('overview');
  const [games, setGames] = useState([]);
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [mobileNavOpen, setMobileNavOpen] = useState(false);
  const [userData, setUserData] = useState(null); // Added to store complete user data

  useEffect(() => {
    if (user) {
      // Use the user data from AuthContext directly
      setUserData(user);
      fetchUserData();
    }
  }, [user]);

  // Fetch user's game data from API
  const fetchUserData = async () => {
    try {
      setLoading(true);

      // Fetch recently played games (this would be from a real API endpoint)
      const recentlyPlayedResponse = await fetch(`${API_URL}/users/recently-played`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // Fetch user's favorites
      const favoritesResponse = await fetch(`${API_URL}/users/favorites`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // Handle responses
      if (recentlyPlayedResponse.ok) {
        const recentData = await recentlyPlayedResponse.json();
        console.log('Recently played data:', recentData);
        setGames(recentData.games || []);
      } else {
        console.warn('Failed to fetch recently played games:', recentlyPlayedResponse.status);
        setGames([]);
      }

      if (favoritesResponse.ok) {
        const favoritesData = await favoritesResponse.json();
        console.log('Favorites data:', favoritesData);
        setFavorites(favoritesData.favorites || []);
      } else {
        console.warn('Failed to fetch favorites:', favoritesResponse.status);
        setFavorites([]);
      }

    } catch (error) {
      console.error('Error fetching user data:', error);
      // Set empty arrays on error
      setGames([]);
      setFavorites([]);
    } finally {
      setLoading(false);
    }
  };

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-[calc(100vh-60px)] bg-gray-900 flex items-center justify-center">
        <LoadingSpinner size="lg" color="primary" showText={true} text="Loading your profile..." />
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  const handleLogout = () => {
    if (window.confirm('Are you sure you want to log out?')) {
      logout();
    }
  };

  const toggleMobileNav = () => {
    setMobileNavOpen(!mobileNavOpen);
  };

  // Render the content based on active section
  const renderContent = () => {
    switch(activeSection) {
      case 'overview':
        return <OverviewSection user={userData || user} games={games} setActiveSection={setActiveSection} createLanguageLink={createLanguageLink} />;
      case 'settings':
        return <SettingsSection user={userData || user} />;
      case 'favorites':
        return <FavoritesSection favorites={favorites} onFavoritesUpdate={fetchUserData} />;
      case 'uploads':
        return <UploadsSection />;
      default:
        return <OverviewSection user={userData || user} games={games} setActiveSection={setActiveSection} createLanguageLink={createLanguageLink} />;
    }
  };

  return (
    <div className="min-h-[calc(100vh-60px)] bg-gray-900 p-5 text-gray-200">
      <div className="max-w-7xl mx-auto flex gap-8 relative">
        {/* Mobile Navigation Toggle */}
        <button 
          className="hidden max-lg:block w-full bg-gray-800 text-white p-4 border-none text-left text-lg font-semibold cursor-pointer rounded mb-5 relative"
          onClick={toggleMobileNav}
        >
          {activeSection} 
          <FaChevronRight className={`absolute right-4 transition-transform duration-300 ${mobileNavOpen ? 'rotate-90' : ''}`} />
        </button>

        {/* Sidebar Navigation */}
        <aside className={`w-72 bg-gray-800 rounded-lg shadow-xl p-5 sticky top-20 h-fit max-lg:fixed max-lg:top-0 max-lg:left-0 max-lg:h-full max-lg:z-50 max-lg:transform max-lg:transition-transform max-lg:duration-300 ${mobileNavOpen ? 'max-lg:translate-x-0' : 'max-lg:-translate-x-full'}`}>
          <div className="flex flex-col items-center py-5 border-b border-gray-600">
            <div className="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mb-4 overflow-hidden border-2 border-gray-600">
              {user.profileImage ? (
                <img src={getSecureImageUrl(user.profileImage)} alt={user.username} className="w-full h-full object-cover" />
              ) : (
                <FaUser className="text-5xl text-gray-500" />
              )}
            </div>
            <h2 className="text-2xl font-bold text-white mb-1">{user.username}</h2>
          </div>
          
          <nav className="mt-5">
            <ul className="list-none p-0">
              <li className="mb-1">
                <button 
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'overview' 
                      ? 'bg-red-500/15 text-red-400 font-semibold' 
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('overview'); setMobileNavOpen(false);}}
                >
                  <FaUser /> Overview
                </button>
              </li>
              <li className="mb-1">
                <button
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'favorites'
                      ? 'bg-red-500/15 text-red-400 font-semibold'
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('favorites'); setMobileNavOpen(false);}}
                >
                  <FaHeart /> Favorites
                </button>
              </li>
              <li className="mb-1">
                <button
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'uploads'
                      ? 'bg-red-500/15 text-red-400 font-semibold'
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('uploads'); setMobileNavOpen(false);}}
                >
                  <FaGamepad /> My Uploads
                </button>
              </li>
              <li className="mb-1">
                <button 
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'settings' 
                      ? 'bg-red-500/15 text-red-400 font-semibold' 
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('settings'); setMobileNavOpen(false);}}
                >
                  <FaCog /> Settings
                </button>
              </li>
              <li className="mt-3">
                <button 
                  className="flex items-center gap-3 w-full p-3 border-none bg-transparent text-red-400 text-left text-base rounded-md cursor-pointer transition-all duration-200 hover:bg-white/5"
                  onClick={handleLogout}
                >
                  <FaSignOutAlt /> Logout
                </button>
              </li>
            </ul>
          </nav>
          
          <div className="mt-8 pt-5 border-t border-gray-600 text-center text-gray-500 text-sm">
            <p>Member since {formatDate(user.joinDate || new Date())}</p>
          </div>
        </aside>

        {/* Main Content Area */}
        <main className="flex-1 bg-gray-800 rounded-lg shadow-xl p-6 min-h-[600px]">
          {loading ? (
            <div className="w-full flex flex-col items-center justify-center py-24">
              <LoadingSpinner size="lg" color="primary" showText={true} text="Loading your profile data..." />
            </div>
          ) : (
            renderContent()
          )}
        </main>
      </div>
    </div>
  );
};





// Helper function to format dates
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};
export default ProfilePage;